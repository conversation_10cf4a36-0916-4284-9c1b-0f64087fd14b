{"name": "project-2023-nlb-gamification-cms", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write ."}, "devDependencies": {"@floating-ui/dom": "^1.6.5", "@iconify-json/material-symbols-light": "^1.1.22", "@skeletonlabs/skeleton": "2.10.0", "@skeletonlabs/tw-plugin": "0.4.0", "@sveltejs/adapter-static": "^3.0.1", "@sveltejs/kit": "^2.5.10", "@sveltejs/vite-plugin-svelte": "^3.1.1", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.13", "@tanstack/match-sorter-utils": "^8.15.1", "@tanstack/svelte-table": "^8.17.3", "@tinymce/tinymce-svelte": "^3.0.0", "@types/node": "20.14.2", "@typescript-eslint/eslint-plugin": "^8.0.0-alpha.30", "@typescript-eslint/parser": "^8.0.0-alpha.30", "autoprefixer": "10.4.19", "axios": "^1.7.2", "buffer": "^6.0.3", "clsx": "^2.1.1", "dayjs": "^1.11.11", "eslint": "^9.4.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.39.3", "postcss": "8.4.38", "prettier": "^3.3.1", "prettier-plugin-svelte": "^3.2.4", "svelte": "^4.2.18", "svelte-check": "^3.8.0", "sveltekit-superforms": "^2.15.1", "tailwind-merge": "^2.3.0", "tailwindcss": "3.4.4", "tslib": "^2.6.3", "typescript": "^5.4.5", "unplugin-icons": "^0.19.0", "vite": "^5.2.13", "vite-plugin-tailwind-purgecss": "0.3.3", "zod": "^3.23.8"}, "type": "module", "dependencies": {"jwt-decode": "^4.0.0"}}