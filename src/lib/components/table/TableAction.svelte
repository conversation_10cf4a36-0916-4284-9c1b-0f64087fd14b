<script lang="ts">
	import { cn } from '$lib';

	export let buttons: Array<{
		label?: string;
		class?: string;
		onClick?: () => Promise<void>;
	}> = [];
</script>

<div class="flex items-center gap-x-4">
	{#each buttons as button}
		<button
			type="button"
			class={cn('btn variant-filled-primary [&>*]:pointer-events-none', button.class)}
			on:click={button.onClick}
		>
			{button.label}
		</button>
	{/each}
</div>
