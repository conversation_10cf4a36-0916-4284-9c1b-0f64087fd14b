<script lang="ts">
	import { goto } from '$app/navigation';

	import type { Paginations } from '$lib/api/common';

	import { Paginator, type PaginationSettings } from '@skeletonlabs/skeleton';
	import { rankItem } from '@tanstack/match-sorter-utils';
	import {
		createSvelteTable,
		flexRender,
		getCoreRowModel,
		getFilteredRowModel,
		getPaginationRowModel,
		type ColumnDef,
		type FilterFn,
		type TableOptions
	} from '@tanstack/svelte-table';
	import { onDestroy } from 'svelte';
	import { writable } from 'svelte/store';

	type T = $$Generic;
	export let data: T[];
	export let columnsBuilder: (params: { globalFilterFn: FilterFn<any> }) => ColumnDef<T>[];
	export let showHeader = true;
	export let showSearch = true;
	export let showPagination = true;
	export let paginations: Paginations;
	export let limit: number;

	let globalFilter = '';
	let paginationSettings: PaginationSettings;
	let globalFilterFn: FilterFn<any> = (row, columnId, value, addMeta) => {
		if (Array.isArray(value)) {
			if (value.length === 0) return true;
			return value.includes(row.getValue(columnId));
		}

		// Rank the item
		const itemRank = rankItem(row.getValue(columnId), value);

		// Store the itemRank info
		addMeta({
			itemRank
		});

		// Return if the item should be filtered in/out
		return itemRank.passed;
	};

	function setGlobalFilter(filter: string) {
		globalFilter = filter;
		options.update((old) => {
			return {
				...old,
				state: {
					...old.state,
					globalFilter: filter
				}
			};
		});
	}

	let timer: NodeJS.Timeout;
	function handleSearch(e: Event) {
		clearTimeout(timer);
		timer = setTimeout(() => {
			const target = e.target as HTMLInputElement;
			setGlobalFilter(target.value);
		}, 300);
	}

	let options = writable<TableOptions<T>>({
		data,
		columns: columnsBuilder({ globalFilterFn }),
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		globalFilterFn: globalFilterFn,
		getPaginationRowModel: getPaginationRowModel(),
		state: {
			globalFilter,
			pagination: {
				pageSize: limit,
				pageIndex: paginations.currentPage - 1
			}
		},
		enableGlobalFilter: true
	});
	$: data, options.update((old) => ({ ...old, data }));

	const table = createSvelteTable(options);
	let headerGroups = $table.getHeaderGroups();

	const calcPaginationSetting = () => {
		paginationSettings = {
			page: paginations.currentPage - 1,
			limit: limit,
			size: paginations.totalCount,
			amounts: [5, 10, 15, 20]
		};
	};

	async function onPageChange(e: CustomEvent<number>) {
		const currentQuery = new URLSearchParams(location.search);
		currentQuery.set('page', (e.detail + 1).toString());
		await goto(`?${currentQuery.toString()}`);
	}

	async function onAmountChange(e: CustomEvent<number>) {
		const currentQuery = new URLSearchParams(location.search);
		currentQuery.set('limit', e.detail.toString());
		await goto(`?${currentQuery.toString()}`);
	}

	$: data, options.update((old) => ({ ...old, data }));
	$: data, globalFilter, calcPaginationSetting();

	onDestroy(() => {
		clearTimeout(timer);
	});
</script>

<section class="space-y-4 flex-1">
	{#if showHeader}
		<div class="flex justify-between items-center">
			{#if showSearch}
				<input type="search" class="input w-96" on:keyup={handleSearch} placeholder="Search..." />
			{:else}
				<div />
			{/if}
			<slot name="header-trail" />
		</div>
	{/if}

	<table class="table table-hover">
		<thead>
			{#each headerGroups as headerGroup}
				<tr>
					{#each headerGroup.headers as header}
						<th colSpan={header.colSpan}>
							{#if !header.isPlaceholder}
								<svelte:component
									this={flexRender(header.column.columnDef.header, header.getContext())}
								/>
							{/if}
						</th>
					{/each}
				</tr>
			{/each}
		</thead>

		<tbody>
			{#each $table.getRowModel().rows as row}
				<tr>
					{#each row.getVisibleCells() as cell}
						<td class="!align-middle">
							<svelte:component this={flexRender(cell.column.columnDef.cell, cell.getContext())} />
						</td>
					{/each}
				</tr>
			{/each}
		</tbody>
	</table>

	{#if showPagination}
		<Paginator
			bind:settings={paginationSettings}
			showFirstLastButtons={false}
			showPreviousNextButtons={true}
			showNumerals={false}
			on:page={onPageChange}
			on:amount={onAmountChange}
		/>
	{/if}
</section>
