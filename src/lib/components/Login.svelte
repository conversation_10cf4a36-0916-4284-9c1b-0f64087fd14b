<script lang="ts">
	import { nlbAuthStore } from '$lib';
	import api from '$lib/api';

	let username = '';
	let password = '';

	const handleLogin = async () => {
		const response = await api.auth.signIn({ username, password });
		nlbAuthStore.set(response);
	};
</script>

<div class="w-screen h-screen flex items-center justify-center">
	<div class="bg-white shadow-xl p-4 rounded-xl max-w-lg w-full">
		<div class="flex flex-col items-center justify-center">
			<h1 class="text-4xl font-bold text-gray-800">NLB CMS</h1>
			<p class="text-gray-600">Please login to continue</p>
		</div>

		<div class="flex flex-col gap-y-2 mt-4">
			<label for="username" class="text-gray-800">Username</label>
			<input
				type="text"
				id="username"
				name="username"
				bind:value={username}
				class="w-full p-2 border border-gray-300 rounded-md text-black"
				placeholder="Enter your username"
			/>

			<label for="password" class="text-gray-800">Password</label>
			<input
				type="password"
				id="password"
				name="password"
				bind:value={password}
				class="w-full p-2 border border-gray-300 rounded-md text-black"
				placeholder="Enter your password"
			/>

			<button type="button" class="btn variant-filled-secondary mt-4" on:click={handleLogin}>
				Login
			</button>
		</div>

		<!-- SAML login method -->
	</div>
</div>
