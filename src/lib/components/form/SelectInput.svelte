<script lang="ts" context="module">
	type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
	import FieldInput from '$lib/components/form/FieldInput.svelte';
	import type { FormPathLeaves } from 'sveltekit-superforms';
	import { formFieldProxy, type SuperForm } from 'sveltekit-superforms/client';
	export let labelClass: HTMLLabelElement['className'] | undefined = undefined;
	export let form: SuperForm<T>;
	export let field: FormPathLeaves<T, string>;
	export let label: string | undefined = undefined;
	export let options: { value: string | boolean; label: string }[] = [];
	const { value, errors } = formFieldProxy(form, field);
</script>

<FieldInput {form} {field} {label} {labelClass}>
	<select
		name={field}
		class="select"
		bind:value={$value}
		aria-invalid={$errors ? 'true' : undefined}
		{...$$restProps}
	>
		{#each options as option}
			<option value={option.value}>{option.label}</option>
		{/each}
	</select>
</FieldInput>
