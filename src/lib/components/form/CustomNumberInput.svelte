<script lang="ts" context="module">
	type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
	import { cn } from '$lib';

	import FieldInput from '$lib/components/form/FieldInput.svelte';

	import type { FormPathLeaves } from 'sveltekit-superforms';
	import { formFieldProxy, type SuperForm } from 'sveltekit-superforms/client';

	export let labelClass: HTMLLabelElement['className'] | undefined = undefined;
	export let form: SuperForm<T>;
	export let field: FormPathLeaves<T, number>;
	export let label: string | undefined = undefined;
	export let plus = ()=>{};
	export let minus = ()=>{};

	const { value, errors } = formFieldProxy(form, field);
</script>

<FieldInput	{form} {field} {label} labelClass={cn(labelClass, { hidden: $$restProps.hidden })}>
	<div class="flex gap-2">
		<button type="button" class="rounded-full bg-slate-600 w-8" on:click={minus}>-</button>
		<input
			class="input text-center"
			name={field}
			type="number"
			aria-invalid={$errors ? 'true' : undefined}
			readonly
			bind:value={$value}
			{...$$restProps}
		/>
		<button type="button" class="rounded-full bg-slate-600 w-8" on:click={plus}>+</button>
	</div>
</FieldInput>
