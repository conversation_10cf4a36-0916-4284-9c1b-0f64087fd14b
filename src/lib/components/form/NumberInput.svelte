<script lang="ts" context="module">
	type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
	import { cn } from '$lib';

	import FieldInput from '$lib/components/form/FieldInput.svelte';

	import type { FormPathLeaves } from 'sveltekit-superforms';
	import { formFieldProxy, type SuperForm } from 'sveltekit-superforms/client';

	export let labelClass: HTMLLabelElement['className'] | undefined = undefined;
	export let form: SuperForm<T>;
	export let field: FormPathLeaves<T, number>;
	export let label: string | undefined = undefined;

	const { value, errors } = formFieldProxy(form, field);
</script>

<FieldInput {form} {field} {label} labelClass={cn(labelClass, { hidden: $$restProps.hidden })}>
	<input
		class="input"
		name={field}
		type="number"
		aria-invalid={$errors ? 'true' : undefined}
		bind:value={$value}
		{...$$restProps}
	/>
</FieldInput>
