<script lang="ts" context="module">
	type T = Record<string, unknown>;
</script>

<script lang="ts" generics="T extends Record<string, unknown>">
	import { cn } from '$lib';

	import type { FormPathLeaves } from 'sveltekit-superforms';
	import { formFieldProxy, type SuperForm } from 'sveltekit-superforms/client';

	export let labelClass: HTMLLabelElement['className'] | undefined = undefined;
	export let form: SuperForm<T>;
	export let field: FormPathLeaves<T, unknown>;
	export let label: string | undefined = undefined;

	const { errors } = formFieldProxy(form, field);
</script>

<label class={cn('relative label w-full', labelClass)}>
	{#if label}<span>{label}</span>{/if}
	<slot />
	{#if $errors}
		<span class="absolute top-full left-0 text-xs text-red-500">
			{$errors}
		</span>
	{/if}
</label>
