<script lang="ts">
	import api from '$lib/api';
	import type { Paginations } from '$lib/api/common';
	import { type MediaItem, MediaType } from '$lib/api/upload';

	export let background:
		| { id: string; name: string; path: string; type: MediaType }
		| null
		| undefined;
	export let mediaId = '';
	export let type: MediaType[] = [MediaType.IMAGE, MediaType.VIDEO];

	let dialogEl: HTMLDialogElement | null = null;
	let media: MediaItem[] = [];
	let paginations: Paginations;
	let page = 1;

	const fetchMedia = async (page: number) => {
		const response = await api.upload.paginate(page, 6, type);
		media = response[0];
		paginations = response[1];
	};

	$: fetchMedia(page);

	const showDialog = () => {
		dialogEl?.showModal();
	};

	const handleSelect = (id: string) => {
		mediaId = id;
		dialogEl?.close();
	};
</script>

<dialog bind:this={dialogEl} class="rounded-xl">
	<div class="p-4 flex flex-col gap-y-4 min-w-96 min-h-96">
		<p class="text-center h3">Media List</p>

		<div class="flex flex-col justify-between flex-1">
			<div class="flex flex-col gap-y-3">
				{#each media as { id, name, path }, index (id)}
					<div class="flex items-center justify-between">
						<p>{name}</p>

						<div class="flex gap-x-2">
							<button
								class="btn btn-sm variant-ghost-tertiary"
								type="button"
								on:click={() => {
									window.open(path, '_blank');
								}}
							>
								Preview
							</button>
							<button
								class="btn btn-sm variant-filled-primary"
								type="button"
								on:click={() => handleSelect(id)}>Select</button
							>
						</div>
					</div>
				{/each}
			</div>
			<div class="flex justify-between">
				<button type="button" class="text-red-500" on:click={() => dialogEl?.close()}>
					Cancel
				</button>

				{#if paginations && (paginations.nextPage || paginations.previousPage)}
					<div class="flex gap-x-4 items-center">
						<button
							class="disabled:text-gray-400"
							type="button"
							disabled={!paginations.previousPage}
							on:click={() => page--}>Previous</button
						>

						<button
							class="disabled:text-gray-400"
							type="button"
							disabled={!paginations.nextPage}
							on:click={() => page++}
						>
							Next
						</button>
					</div>
				{/if}
			</div>
		</div>
	</div>
</dialog>

<div class="flex flex-col gap-y-2">
	<p>Background</p>
	{#if background && background.id === mediaId}
		{#if background.type === 'VIDEO'}
			<video class="h-32 object-contain rounded-lg" autoplay loop muted>
				<source src={background.path} type="video/mp4" />
				<track kind="captions" />
			</video>
		{:else}
			<img src={background.path} alt={background.name} class="h-32 object-contain rounded-lg" />
		{/if}
	{/if}
	{#if !background || background.id !== mediaId}
		{#if media.find((m) => m.id === mediaId)?.type === 'VIDEO'}
			<video class="h-32 object-contain rounded-lg" autoplay loop muted>
				<source src={media.find((m) => m.id === mediaId)?.path} type="video/mp4" />
				<track kind="captions" />
			</video>
		{:else}
			<img
				src={media.find((m) => m.id === mediaId)?.path}
				alt={media.find((m) => m.id === mediaId)?.name}
				class="h-32 object-contain rounded-lg"
			/>
		{/if}
	{/if}
	{#if !mediaId && !background}
		<p class="text-sm text-gray-400">No media selected</p>
	{/if}

	<button class="btn btn-sm variant-filled-success" type="button" on:click={showDialog}>
		{mediaId ? 'Change Media' : 'Select Media'}
	</button>
</div>
