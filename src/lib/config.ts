import IconCharacter from 'virtual:icons/material-symbols-light/cruelty-free';
import IconApplication from 'virtual:icons/material-symbols-light/developer-mode-tv-outline-rounded';
import IconHome from 'virtual:icons/material-symbols-light/space-dashboard';
import IconQuiz from 'virtual:icons/material-symbols-light/quiz';
import IconUser from 'virtual:icons/material-symbols-light/supervisor-account-outline';
import IconMedia from 'virtual:icons/material-symbols-light/perm-media';
import IconColor from 'virtual:icons/material-symbols-light/palette'

export const NAVIGATION = [
	{
		title: 'Dashboard',
		Icon: IconHome,
		href: '/',
		hidden: false
	},
	{
		title: 'Application',
		Icon: IconApplication,
		href: '/application/list',
		hidden: false
	},
	{
		title: 'User',
		Icon: IconUser,
		href: '/user/list',
		hidden: false
	},
	{
		title: 'Character',
		Icon: IconCharacter,
		href: '/character/list',
		hidden: false
	},
	{
		title: 'Color',
		Icon: IconColor,
		href: '/color/list',
		hidden: false
	},
	{
		title: 'Media',
		Icon: IconMedia,
		href: '/media/list',
		hidden: false
	},
	{
		title: 'FAQ',
		Icon: IconQuiz,
		href: '/faq/list',
		hidden: false
	}
];

export const APP_DEPENDS: Record<string, `${string}:${string}`> = {
	listApplication: 'nlb:list-application',
	updateApplication: 'nlb:update-application',
	listCharacter: 'nlb:list-character',
	listColor: 'nlb:list-color',
	listFaq: 'nlb:list-faq',
	listProjectionsVideos: 'nlb:list-projections-videos',
};
