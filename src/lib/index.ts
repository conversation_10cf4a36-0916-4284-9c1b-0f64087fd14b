import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { persisted } from './helpers/persistedStore';
import type { DecodedToken } from './api/auth';
import { writable } from 'svelte/store';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export function camelCaseToWords(s: string) {
	const result = s.replace(/([A-Z])/g, ' $1');
	return result.charAt(0).toUpperCase() + result.slice(1);
}

export const nlbAuthStore = writable<{ accessToken: string; decodedToken?: DecodedToken }>({
	accessToken: ''
});
