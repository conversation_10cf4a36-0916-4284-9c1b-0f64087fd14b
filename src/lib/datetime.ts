import dayjs, { type DayjsTimezone } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

dayjs.tz.setDefault('Asia/Singapore');

export function formatDate(date: Parameters<DayjsTimezone>[0], template: string = 'DD-MM-YYYY') {
	return dayjs.tz(date).format(template);
}

export const dayjsTz = dayjs.tz;
