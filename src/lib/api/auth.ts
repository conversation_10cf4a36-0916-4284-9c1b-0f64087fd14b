import { type AxiosInstance } from 'axios';
import { z } from 'zod';
import { jwtDecode } from 'jwt-decode';

export const signInSchema = z.object({
	username: z.string(),
	password: z.string()
});

export type AuthenticatedResponse = {
	accessToken: string;
};

export type DecodedToken = {
	sub: string;
	username: string;
	role: string;
	iat: number;
	exp: number;
};

export type SignIn = z.infer<typeof signInSchema>;

export type SamlAuthResponse = {
	accessToken: string;
	decodedToken: DecodedToken;
};

export const AuthService = ({ client }: { client: AxiosInstance }) => {
	const PATH = 'auth';

	return {
		async signIn(dto: SignIn) {
			const { data } = await client.post<AuthenticatedResponse>(`${PATH}/sign-in`, dto);

			const decodedToken = jwtDecode(data.accessToken) as DecodedToken;

			return {
				...data,
				decodedToken
			};
		},

		async generateApiKey(appId: string) {
			const { data } = await client.post<{ key: string }>(`${PATH}/generate-api-key`, { appId });
			return data;
		},

		async revokeApiKey(appId: string) {
			await client.post(`${PATH}/revoke-api-key`, { appId });
		},

		async signout() {
			await client.post(`${PATH}/sign-out`);
		},

		async handleSamlCallback(samlResponse: string): Promise<SamlAuthResponse> {
			const { data } = await client.post<AuthenticatedResponse>(`${PATH}/saml/callback`, {
				samlResponse
			});

			const decodedToken = jwtDecode(data.accessToken) as DecodedToken;

			return {
				...data,
				decodedToken
			};
		},

		async getSamlMetadata() {
			const { data } = await client.get(`${PATH}/saml/metadata`);
			return data;
		}
	};
};
