import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { timestampSchema, type Paginations } from './common';

export const faqCRUDSchema = z.object({
	id: z.string().optional(),
	question: z.string().min(1, 'Question cannot be empty'),
	answer: z.string().min(1, 'Answer cannot be empty'),
	order: z.number().default(0)
});
export const faqSchema = faqCRUDSchema
	.extend({ id: z.string(), order: z.number() })
	.merge(timestampSchema);

export type Faq = z.infer<typeof faqSchema>;
export type FaqCRUD = z.infer<typeof faqCRUDSchema>;

export const FaqService = ({ client }: { client: AxiosInstance }) => {
	const PATH = 'faq';

	return {
		async create(dto: FaqCRUD) {
			const { data } = await client.post<Faq>(`${PATH}`, dto);
			return data;
		},

		async findAll(page = 1, limit = 10) {
			const { data } = await client.get<[Faq[], Paginations]>(
				`${PATH}?page=${page}&limit=${limit}`
			);
			return data;
		},

		async findOne(id: string) {
			const { data } = await client.get<Faq>(`${PATH}/${id}`);
			return data;
		},

		async update(id: string, dto: FaqCRUD) {
			const { data } = await client.patch<Faq>(`${PATH}/${id}`, dto);
			return data;
		},

		async updateOrder(id: string, order: number) {
			const { data } = await client.patch<Faq>(`${PATH}/${id}/order?newOrder=${order}`);
			return data;
		},

		async delete(id: string) {
			const { data } = await client.delete<string>(`${PATH}/${id}`);
			return data;
		}
	};
};
