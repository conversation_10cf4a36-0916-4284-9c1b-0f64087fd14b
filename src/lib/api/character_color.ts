import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { timestampSchema, type Paginations } from './common';
import { colorSchema, type Color } from './color';

export enum ColorType { REGULAR = "REGULAR", PREMIUM = "PREMIUM" };
export const characterColorCRUDSchema = z.object({
    characterId: z.string().min(1, "Character ID can't empty"),
    colorId: z.string().min(1, "Character ID can't empty"),
    type: z.nativeEnum(ColorType)
});
export const characterColorSchema = characterColorCRUDSchema
    .extend({ characterId: z.string(), colorId: z.string() })
    .merge(timestampSchema);
export type CharacterColor = z.infer<typeof characterColorSchema>;
export type CharacterColorCRUD = z.infer<typeof characterColorCRUDSchema>;

export const CharacterColorService = ({ client }: { client: AxiosInstance }) => {
    const CHARACTER_PATH = 'character';

    return {
        async assign(dto: Omit<CharacterColorCRUD, "color">) {
            return await client.post<CharacterColor>(`${CHARACTER_PATH}/color/assign`, dto);
        },

        async findAll(id: string) {
            const { data } = await client.get<{ type: ColorType[], characterColors: CharacterColorCRUD[], colors: Color[] }>(`${CHARACTER_PATH}/${id}/colors`);
            return data;
        },

        async unassign(dto: Pick<CharacterColorCRUD, "characterId" | "colorId">) {
            return await client.post<string>(`${CHARACTER_PATH}/color/unassign`, dto);
        }
    };
};
