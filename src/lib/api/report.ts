import { type AxiosInstance } from 'axios';

export type StatisticDetail = {
	pressStartButton: number;
	submitCustomization: number;
	idleTimeout: number;
	numberOfKioskCharactersCustomizedPerHour: Record<string, number>;
	chosenCharacter: number;
	numberOfKioskCharactersGroupByName: Record<string, number>;
	numberOfKioskCharactersQueued: number;
	totalTimeSpentToCustomizeCharacterInSeconds: Record<string, number>;

	physicalCustomizedAmountPerHour: Record<string, number>;
	createdPhysicalCharacterGroupByName: Record<string, number>;
	numberOfCharacterFromPhysicalPaperQueued: number;

	numberOfWebAppCharacterQueued: number;
	numberOfWebAppCharatersDisplayedPerHour: Record<string, number>;
	numberOfWebAppCharactersDisplayedGroupByName: Record<string, number>;
	webAppScanQRCodeAmount: number;
	webAppScanQRCodeAmountUniqueUser: number;
	webAppCollectedCharacterGroupByPrice: Record<string, number>;
	socialShareAmountTaggedToLibrary: number;
	socialShareAmountUntaggedToLibrary: number;
	totalPointsPerDay: number;
};
export type Statistic = {
	kiosk: Record<string, StatisticDetail>;
	webapp: Record<string, StatisticDetail>;
	physical: Record<string, StatisticDetail>;
};
export type StatisticFilterDto = {
	appId: string;
	from: Date;
	to: Date;
};

export const ReportService = ({ client }: { client: AxiosInstance }) => {
	return {
		async generate(dto: StatisticFilterDto) {
			const { data } = await client.post<Statistic>('report', dto);
			return data;
		},
		async downloadExcel(dto: StatisticFilterDto) {
			const { data } = await client.post<{ file: Buffer; pwd: string }>('report/excel', dto);
			return data;
		},
		async downloadExcelV2(dto: StatisticFilterDto) {
			const { data } = await client.post<{ file: Buffer; pwd: string }>('report/v2/excel', dto);
			return data;
		}
	};
};
