import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { timestampSchema, type Paginations } from './common';

export const userCRUDSchema = z.object({
	id: z.string().optional(),
	username: z.string(),
	role: z.string(),
	pdvId: z.number(),
	deactivatedAt: z.string().nullable(),
	createdAt: z.string(),
	updatedAt: z.string()
});
export const userSchema = userCRUDSchema.extend({ id: z.string() }).merge(timestampSchema);

export type User = z.infer<typeof userSchema>;
export type UserCRUD = z.infer<typeof userCRUDSchema>;

export const UserService = ({ client }: { client: AxiosInstance }) => {
	const PATH = 'user';

	return {
		async create(dto: UserCRUD) {
			const { data } = await client.post<User>(`${PATH}`, dto);
			return data;
		},

		async findAll(page = 1, limit = 10) {
			const { data } = await client.get<[User[], Paginations]>(
				`${PATH}?page=${page}&limit=${limit}&roles=EXTERNAL_USER`
			);
			return data;
		},

		async findOne(id: string) {
			const { data } = await client.get<User>(`${PATH}/${id}`);
			return data;
		},

		async update(id: string, dto: Partial<UserCRUD>) {
			const { data } = await client.patch<User>(`${PATH}/${id}`, dto);
			return data;
		},

		async delete(id: string) {
			const { data } = await client.delete<string>(`${PATH}/${id}`);
			return data;
		}
	};
};
