import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { characterSettingSchema } from './character';
import { timestampSchema, type Paginations } from './common';
import { MediaType } from './upload';

export const applicationCRUDSchema = z.object({
	id: z
		.string()
		.min(3, 'Application id must be at least 3 characters')
		.refine((s) => !s.includes(' '), 'Application id not allow spaces'),
	name: z
		.string()
		.min(3, 'Application name must be at least 3 characters')
		.max(50, 'Application name must be at most 50 characters')
});
export const applicationSchema = applicationCRUDSchema.merge(timestampSchema);
export type Application = z.infer<typeof applicationSchema>;
export type ApplicationCRUD = z.infer<typeof applicationCRUDSchema>;

export const settingCRUDSchema = z
	.object({
		background: z
			.object({ id: z.string(), name: z.string(), path: z.string(), type: z.nativeEnum(MediaType) })
			.nullish(),
		backgroundId: z.string(),
		appId: z.string(),
		projectionLockTimeout: z.number().min(1, 'Projection lock timeout must be at least 1 second'),
		maxProjectionCharacterPerAccount: z.number().min(1, 'Max projection character per account must be at least 1 character'),
		minCharacters: z.number().min(1, 'Min character must be at least 1 character'),
		maxCharacters: z.number().min(1, 'Max character must be at least 1 character'),
		ttlCharacter: z.number().min(1, 'Time to live must be at least 1 second')
	})
	.merge(characterSettingSchema);
export const settingSchema = settingCRUDSchema.extend({ id: z.string() }).merge(timestampSchema);
export type Setting = z.infer<typeof settingSchema>;
export type SettingCRUD = z.infer<typeof settingCRUDSchema>;

export type ProjectionStatus = {
	queueLength: number;
	totalCharacters: number;
	activeCharacters: number;
};

export const ApplicationService = ({ client }: { client: AxiosInstance }) => {
	const APPLICATION_PATH = 'application';
	const SETTING_PATH = 'settings';

	return {
		async create(dto: ApplicationCRUD) {
			const { data } = await client.post<Pick<Application, 'id'>>(`${APPLICATION_PATH}`, dto);
			return data;
		},

		async findAll(page = 1, limit = 10) {
			const { data } = await client.get<[Application[], Paginations]>(
				`${APPLICATION_PATH}?page=${page}&limit=${limit}`
			);
			return data;
		},

		async findOne(id: string) {
			const { data } = await client.get<Application>(`${APPLICATION_PATH}/${id}`);
			return data;
		},

		async update(id: string, dto: Pick<ApplicationCRUD, 'name'>) {
			const { data } = await client.patch<Pick<Application, 'id'>>(
				`${APPLICATION_PATH}/${id}`,
				dto
			);
			return data;
		},

		async findOneSettings(appId: string) {
			const { data } = await client.get<Setting>(`${APPLICATION_PATH}/${appId}/${SETTING_PATH}`);
			return data;
		},

		async updateSettings(appId: string, dto: Omit<SettingCRUD, 'appId'>) {
			const { data } = await client.patch<Setting>(
				`${APPLICATION_PATH}/${appId}/${SETTING_PATH}`,
				dto
			);

			return data;
		},

		async projectionStatus(appId: string) {
			const { data } = await client.get<ProjectionStatus>(
				`${APPLICATION_PATH}/${appId}/projection/status`
			);

			return data;
		},

		async flushCharacters(appId: string) {
			const { data } = await client.put<ProjectionStatus>(
				`${APPLICATION_PATH}/${appId}/characters/flush`
			);

			return data;
		},

		async flushQueue(appId: string) {
			const { data } = await client.put<ProjectionStatus>(
				`${APPLICATION_PATH}/${appId}/queue/flush`
			);

			return data;
		}
	};
};
