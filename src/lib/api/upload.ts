import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import type { Paginations } from './common';

export enum MediaType {
	IMAGE = 'IMAGE',
	VIDEO = 'VIDEO'
}

export const uploadSchema = z.object({
	name: z.string(),
	type: z.string(),
	file: z
		.instanceof(File, { message: 'Please upload a file.' })
		.refine((f) => f.size < 100_000_000, 'Max file size is 100MB')
});

export type MediaItem = {
	id: string;
	name: string;
	path: string;
	type: MediaType;
};

export type UploadSchema = z.infer<typeof uploadSchema>;

export const UploadService = ({ client }: { client: AxiosInstance }) => {
	const PATH = 'upload';

	return {
		async paginate(page = 1, limit = 10, type?: MediaType[]) {
			let url = `${PATH}?page=${page}&limit=${limit}`;

			if (type) {
				const types = type.join(',');
				url += `&type=${types}`;
			}

			const { data } = await client.get<[MediaItem[], Paginations]>(url);
			return data;
		},

		async create(dto: UploadSchema) {
			const formData = new FormData();
			formData.append('name', dto.name);
			formData.append('type', dto.type.toString());
			formData.append('file', dto.file);

			const { data } = await client.post<string>(`${PATH}`, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			});

			return data;
		},

		async delete(id: string) {
			const { data } = await client.delete<boolean>(`${PATH}/${id}`);
			return data;
		}
	};
};
