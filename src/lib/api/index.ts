import { PUBLIC_BACKEND_URL } from '$env/static/public';
import { nlbAuthStore } from '$lib';
import { ApplicationService } from '$lib/api/application';
import axios, { AxiosError } from 'axios';
import { AuthService } from './auth';
import { CharacterService } from './character';
import { FaqService } from './faq';
import { HealthService } from './heath';
import { ReportService } from './report';
import { UserService } from './user';
import { UploadService } from './upload';
import { ColorService } from './color';
import { CharacterColorService } from './character_color';

const axiosInstance = axios.create({
	baseURL: PUBLIC_BACKEND_URL,
	headers: {
		'Content-Type': 'application/json'
	}
});

nlbAuthStore.subscribe((value) => {
	axiosInstance.defaults.headers.Authorization = `Bearer ${value.accessToken}`;
});

axiosInstance.interceptors.response.use(
	(response) => {
		return response;
	},
	(error) => {
		if (error instanceof AxiosError) {
			if (error.response?.status === 401) {
				nlbAuthStore.set({ accessToken: '' });
			}
		}

		return Promise.reject(error.message);
	}
);

export default {
	auth: AuthService({ client: axiosInstance }),
	health: HealthService({ client: axiosInstance }),
	application: ApplicationService({ client: axiosInstance }),
	character: CharacterService({ client: axiosInstance }),
	color: ColorService({ client: axiosInstance }),
	character_color: CharacterColorService({ client: axiosInstance }),
	user: UserService({ client: axiosInstance }),
	faq: FaqService({ client: axiosInstance }),
	upload: UploadService({ client: axiosInstance }),
	report: ReportService({ client: axiosInstance })
};
