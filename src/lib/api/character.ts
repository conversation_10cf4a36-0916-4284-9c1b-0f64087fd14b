import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { timestampSchema, type Paginations } from './common';

export const characterCRUDSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, "Character name can't empty"),
	description: z.string().max(50, 'Description must be less than 50 characters').nullish(),
	spawn: z.string(),
	movement: z.string(),
	faceDirection: z.string(),
	price: z.number().min(20)
});
export const characterSchema = characterCRUDSchema
	.extend({ id: z.string() })
	.merge(timestampSchema);
export const characterSettingSchema = z.object({
	size: z.number().min(0.1, 'Character size must be at least 0.1'),
	speed: z.number().min(0.1, 'Character speed must be at least 0.1')
});
export type Character = z.infer<typeof characterSchema>;
export type CharacterCRUD = z.infer<typeof characterCRUDSchema>;
export type CharacterSetting = z.infer<typeof characterSettingSchema>;

export const CharacterService = ({ client }: { client: AxiosInstance }) => {
	const CHARACTER_PATH = 'character';

	return {
		async create(dto: CharacterCRUD) {
			const { data } = await client.post<Character>(`${CHARACTER_PATH}`, dto);
			return data;
		},

		async findAll(page = 1, limit = 10) {
			const { data } = await client.get<[Character[], Paginations]>(
				`${CHARACTER_PATH}?page=${page}&limit=${limit}`
			);
			return data;
		},

		async findOne(id: string) {
			const { data } = await client.get<Character>(`${CHARACTER_PATH}/${id}`);
			return data;
		},

		async update(id: string, dto: Pick<CharacterCRUD, 'name'>) {
			const { data } = await client.patch<Character>(`${CHARACTER_PATH}/${id}`, dto);
			return data;
		},

		async delete(id: string) {
			const { data } = await client.delete<string>(`${CHARACTER_PATH}/${id}`);
			return data;
		}
	};
};
