import type { AxiosInstance } from 'axios';
import { z } from 'zod';
import { timestampSchema, type Paginations } from './common';

export const colorCRUDSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1, "Name can't empty"),
	hex: z.string()
});
export const colorSchema = colorCRUDSchema.extend({ id: z.string() }).merge(timestampSchema);
export type Color = z.infer<typeof colorSchema>;
export type ColorCRUD = z.infer<typeof colorCRUDSchema>;

export const ColorService = ({ client }: { client: AxiosInstance }) => {
	const COLOR_PATH = 'character/color';

	return {
		async create(dto: ColorCRUD) {
			const { data } = await client.post<Color>(`${COLOR_PATH}`, dto);
			return data;
		},

		async findAll(page = 1, limit = 10) {
			const { data } = await client.get<[Color[], Paginations]>(
				`${COLOR_PATH}?page=${page}&limit=${limit}`
			);
			return data;
		},

		async findOne(id: string) {
			const { data } = await client.get<Color>(`${COLOR_PATH}/${id}`);
			return data;
		},

		async update(id: string, dto: Pick<ColorCRUD, 'name'>) {
			const { data } = await client.patch<Color>(`${COLOR_PATH}/${id}`, dto);
			return data;
		},

		async delete(id: string) {
			const { data } = await client.delete<string>(`${COLOR_PATH}/${id}`);
			return data;
		}
	};
};
