import { AxiosError, type AxiosInstance } from 'axios';

export type HealthCheck = {
	status: 'ok' | 'error';
	info: {
		[name: string]:
			| {
					status: 'up';
			  }
			| undefined;
	};
	error: {
		[name: string]:
			| {
					status: 'down';
			  }
			| undefined;
	};
	details: {
		[name: string]: {
			status: 'up' | 'down';
		};
	};
};

export const HealthService = ({ client }: { client: AxiosInstance }) => {
	return {
		async statusCheck() {
			try {
				const { data } = await client.get<HealthCheck>('health');
				return data;
			} catch (error: unknown) {
				if (error instanceof AxiosError) {
					return error.message;
				}

				return error as string;
			}
		}
	};
};
