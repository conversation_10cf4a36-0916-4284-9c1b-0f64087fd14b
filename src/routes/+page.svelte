<script lang="ts">
	import { cn } from '$lib';

	export let data;
	let { healthStatus } = data;
</script>

<div class="h-full w-full">
	<section class="flex flex-col gap-4">
		<h3 class="h3">Systems Status</h3>

		<div class="grid grid-cols-4 gap-4 select-none">
			{#each Object.entries(healthStatus.details) as item}
				<div class="card card-hover p-4 flex justify-between">
					<span>{item[0]}</span>
					<span
						class={cn('font-semibold text-green-500', {
							'text-red-500': item[1].status === 'down'
						})}
					>
						{#if item[1].status === 'up'}
							OK
						{:else}
							Down
						{/if}
					</span>
				</div>
			{/each}
		</div>
	</section>
</div>
