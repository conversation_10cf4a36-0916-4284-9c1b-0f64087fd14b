<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { nlbAuthStore } from '$lib';

	let loading = true;
	let error = '';

	onMount(async () => {
		try {
			// Check URL parameters for authentication result
			const token = $page.url.searchParams.get('token');
			const accessToken = $page.url.searchParams.get('accessToken');
			const errorParam = $page.url.searchParams.get('error');
			const success = $page.url.searchParams.get('success');

			if (errorParam) {
				throw new Error(decodeURIComponent(errorParam));
			}

			// Handle different backend response patterns
			const authToken = token || accessToken;

			if (authToken) {
				// Backend provided token in URL parameters
				const authData: { accessToken: string; decodedToken?: any } = {
					accessToken: authToken
				};

				// Try to decode token if it's a JWT
				try {
					const { jwtDecode } = await import('jwt-decode');
					const decodedToken = jwtDecode(authToken);
					authData.decodedToken = decodedToken;
				} catch (decodeError) {
					console.warn('Could not decode token:', decodeError);
				}

				nlbAuthStore.set(authData);
				await goto('/');
			} else if (success === 'true') {
				// Backend indicates success but handles token via cookies/session
				// Redirect to main app and let the auth guard handle validation
				await goto('/');
			} else {
				// No clear success indicator, try to redirect and let auth validation handle it
				await goto('/');
			}
		} catch (err) {
			console.error('SAML authentication failed:', err);
			error = err instanceof Error ? err.message : 'Authentication failed';
			loading = false;
		}
	});
</script>

<div class="w-screen h-screen flex items-center justify-center">
	<div class="bg-white shadow-xl p-8 rounded-xl max-w-md w-full text-center">
		{#if loading}
			<div class="flex flex-col items-center gap-4">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
				<h2 class="text-xl font-semibold text-gray-800">Authenticating...</h2>
				<p class="text-gray-600">Please wait while we verify your credentials.</p>
			</div>
		{:else if error}
			<div class="flex flex-col items-center gap-4">
				<div class="text-red-500 text-4xl">⚠️</div>
				<h2 class="text-xl font-semibold text-red-600">Authentication Failed</h2>
				<p class="text-gray-600">{error}</p>
				<button 
					class="btn variant-filled-secondary mt-4" 
					on:click={() => goto('/login')}
				>
					Back to Login
				</button>
			</div>
		{/if}
	</div>
</div>
