<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { nlbAuthStore } from '$lib';
	import api from '$lib/api';

	let loading = true;
	let error = '';

	onMount(async () => {
		try {
			// Get SAML response from URL parameters
			const samlResponse = $page.url.searchParams.get('SAMLResponse');
			
			if (!samlResponse) {
				throw new Error('No SAML response found');
			}

			// Send SAML response to backend for validation
			const authResponse = await api.auth.handleSamlCallback(samlResponse);
			
			// Store the authentication data
			nlbAuthStore.set(authResponse);
			
			// Redirect to main application
			await goto('/');
		} catch (err) {
			console.error('SAML authentication failed:', err);
			error = err instanceof Error ? err.message : 'Authentication failed';
			loading = false;
		}
	});
</script>

<div class="w-screen h-screen flex items-center justify-center">
	<div class="bg-white shadow-xl p-8 rounded-xl max-w-md w-full text-center">
		{#if loading}
			<div class="flex flex-col items-center gap-4">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
				<h2 class="text-xl font-semibold text-gray-800">Authenticating...</h2>
				<p class="text-gray-600">Please wait while we verify your credentials.</p>
			</div>
		{:else if error}
			<div class="flex flex-col items-center gap-4">
				<div class="text-red-500 text-4xl">⚠️</div>
				<h2 class="text-xl font-semibold text-red-600">Authentication Failed</h2>
				<p class="text-gray-600">{error}</p>
				<button 
					class="btn variant-filled-secondary mt-4" 
					on:click={() => goto('/login')}
				>
					Back to Login
				</button>
			</div>
		{/if}
	</div>
</div>
