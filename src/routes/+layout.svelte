<script lang="ts">
	import { afterNavigate } from '$app/navigation';
	import { page } from '$app/stores';
	import { nlbAuthStore } from '$lib';
	import Login from '$lib/components/Login.svelte';
	import { NAVIGATION } from '$lib/config';
	import { arrow, autoUpdate, computePosition, flip, offset, shift } from '@floating-ui/dom';
	import {
		AppBar,
		AppRail,
		AppRailAnchor,
		AppShell,
		Toast,
		initializeStores,
		storePopup,
		Modal
	} from '@skeletonlabs/skeleton';
	import { onMount } from 'svelte';
	import IconSignOut from 'virtual:icons/material-symbols-light/exit-to-app';
	import '../app.postcss';
	import api from '$lib/api';

	initializeStores();
	storePopup.set({ computePosition, autoUpdate, offset, shift, flip, arrow });

	$: isAuthenticated = !!$nlbAuthStore.accessToken;

	const validateToken = async () => {
		if (
			!$nlbAuthStore.accessToken ||
			!$nlbAuthStore.decodedToken ||
			$nlbAuthStore.decodedToken.exp < Date.now() / 1000 ||
			$nlbAuthStore.decodedToken.role !== 'ADMIN'
		) {
			signout();
		}
	};

	const signout = async () => {
		await api.auth.signout();
		nlbAuthStore.set({ accessToken: '' });
	};

	// const modalRegistry: Record<string, ModalComponent> = {
	// 	// Set a unique modal ID, then pass the component reference
	// };

	onMount(() => {
		validateToken();
	});

	afterNavigate(() => {
		validateToken();
	});
</script>

<Toast position="t" transitions={true} />
<Modal />
<!-- App Shell -->

{#if !isAuthenticated}
	<Login />
{:else}
	<AppShell slotPageContent="p-4 select-none">
		<svelte:fragment slot="sidebarLeft">
			<AppRail>
				<svelte:fragment slot="lead">
					<AppRailAnchor title="NLB">
						<h4 class="h4 select-none">NLB CMS</h4>
					</AppRailAnchor>
				</svelte:fragment>

				{#each NAVIGATION as { title, Icon, href, hidden }}
					{#if !hidden}
						<AppRailAnchor
							{href}
							{title}
							selected={$page.url.pathname.split('/')[1] === href.split('/')[1]}
						>
							<svelte:fragment slot="lead">
								<Icon />
							</svelte:fragment>
							<span class="whitespace-pre-line select-none">{title}</span>
						</AppRailAnchor>
					{/if}
				{/each}

				<svelte:fragment slot="trail">
					<AppRailAnchor href="/" title="Logout" on:click={signout}>
						<svelte:fragment slot="lead">
							<IconSignOut class="nlb-icon text-error-400-500-token" />
						</svelte:fragment>
						<button class="text-error-400-500-token select-none"> Sign Out </button>
					</AppRailAnchor>
				</svelte:fragment>
			</AppRail>
		</svelte:fragment>
		<svelte:fragment slot="pageHeader">
			<AppBar padding="p-8"></AppBar>
		</svelte:fragment>

		<section class="w-full h-full flex flex-col gap-y-2 overflow-hidden">
			<slot />
		</section>
	</AppShell>
{/if}
