<script lang="ts">
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import type { PageData } from './$types';
	import type { Character } from '$lib/api/character';
	import { formatDate } from '$lib/datetime';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { goto, invalidate } from '$app/navigation';
	import Table from '$lib/components/table/Table.svelte';
	import type { User } from '$lib/api/user';
	import api from '$lib/api';

	export let data: PageData;
	let { users, paginations, limit } = data;
	$: ({ users, paginations, limit } = data);

	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<User>;
	}): ColumnDef<User>[] => [
		{
			accessorKey: 'username',
			header: 'Pdv ID',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'role',
			header: 'Role',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'deactivatedAt',
			header: 'Deactivated At',
			cell: (info) => (info.getValue() ? formatDate(info.getValue<Date>()) : '---'),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'createdAt',
			header: 'Created At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			accessorKey: 'updatedAt',
			header: 'Last Updated At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) => {
				const active = info.row.original.deactivatedAt === null;

				return flexRender(TableAction, {
					buttons: [
						{
							label: active ? 'Deactivate' : 'Activate',
							class: active ? 'variant-filled-error' : 'variant-filled-success',
							onClick: async () => {
								if (active) {
									const confirmed = confirm('Are you sure you want to deactivate this user?');
									if (!confirmed) return;
								} else {
									const confirmed = confirm('Are you sure you want to activate this user?');
									if (!confirmed) return;
								}

								await api.user.update(info.row.original.id, {
									deactivatedAt: active ? new Date().toISOString() : null
								});

								await invalidate('user:list');
							}
						}
					]
				});
			}
		}
	];
</script>

<Table data={users} {columnsBuilder} {paginations} bind:limit></Table>
