import api from '$lib/api';
import { colorCRUDSchema, type ColorCRUD } from '$lib/api/color';
import { APP_DEPENDS } from '$lib/config';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async ({ depends, params }) => {
	depends(APP_DEPENDS.updateApplication);

	const data: ColorCRUD = {
		name: '',
		hex: '#000000'
	};

	const { id, name, hex } = await api.color.findOne(params.id);
	Object.assign(data, { id, name, hex });

	const colorForm = await superValidate(data, zod(colorCRUDSchema));

	return { colorForm };
};
