<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { colorCRUDSchema, type ColorCRUD } from '$lib/api/color';
	import ColorPicker from '$lib/components/form/ColorPicker.svelte';
	import InputWrapper from '$lib/components/form/InputWrapper.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';

	export let formType: 'create' | 'update' = 'create';
	export let superValidated: SuperValidated<ColorCRUD>;

	const toastStore = getToastStore();

	const sForm = superForm(superValidated, {
		SPA: true,
		validators: zod(colorCRUDSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					switch (formType) {
						case 'create': {
							await api.color.create(data);
							break;
						}

						case 'update': {
							const { id, ...dto } = data;
							await api.color.update(id!, dto);
							break;
						}
					}

					await invalidate(APP_DEPENDS.listColor);
					await goto('/color/list');
					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
						timeout: 3000
					});
				}
			} catch (error) {
				return toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Something went wrong. Cannot submit form at the moment.',
					timeout: 3000
				});
			}
		}
	});
	const { enhance, tainted } = sForm;
</script>

<form class="h-full flex flex-col gap-y-4 p-2" method="POST" use:enhance>
	<TextInput form={sForm} field="id" label="id" hidden />

	<InputWrapper>
		<TextInput form={sForm} field="name" label="Name" />
		<ColorPicker form={sForm} field="hex" label="Color" />
	</InputWrapper>

	<button class="btn variant-filled-success" disabled={!$tainted}>Save Changes</button>
</form>
