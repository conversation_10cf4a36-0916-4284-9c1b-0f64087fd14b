<script lang="ts">
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import type { PageData } from './$types';
	import type { Color } from '$lib/api/color';
	import { formatDate } from '$lib/datetime';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { goto, invalidate } from '$app/navigation';
	import Table from '$lib/components/table/Table.svelte';
	import api from '$lib/api';
	import { APP_DEPENDS } from '$lib/config';
	import { getModalStore, type ModalSettings } from '@skeletonlabs/skeleton';

	export let data: PageData;
	let { colors, paginations, limit } = data;
	$: ({ colors, paginations, limit } = data);
	const modalStore = getModalStore();
	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<Color>;
	}): ColumnDef<Color>[] => [
		{
			accessorKey: 'name',
			header: 'Name',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'createdAt',
			header: 'Created At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			accessorKey: 'updatedAt',
			header: 'Last Updated At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) =>
				flexRender(TableAction, {
					buttons: [
						{
							label: 'Details',
							class: 'variant-filled-warning',
							onClick: async () => {
								goto(`/color/${info.row.original.id}`);
							}
						},
						{
							label: 'Delete',
							class: 'variant-filled-error',
							onClick: async () => {
								const modal: ModalSettings = {
									type: 'confirm',
									// Data
									title: 'Please Confirm',
									body: 'Are you sure you wish to proceed?',
									// TRUE if confirm pressed, FALSE if cancel pressed
									response: async (r: boolean) => {
										if (r) {
											await api.color.delete(info.row.original.id);
											await invalidate(APP_DEPENDS.listColor);
										}
									}
								};
								modalStore.trigger(modal);
							}
						}
					]
				})
		}
	];
</script>

<Table data={colors} {columnsBuilder} {paginations} bind:limit>
	<svelte:fragment slot="header-trail">
		<button type="button" class="btn variant-filled-primary" on:click={() => goto('/color/create')}>
			New Color
		</button>
	</svelte:fragment>
</Table>
