import api from '$lib/api';
import { APP_DEPENDS } from '$lib/config.js';

export const prerender = 'auto';
export const load = async ({ url, depends }) => {
	depends(APP_DEPENDS.listColor);
	const page = url.searchParams.get('page') || 1;
	const limit = url.searchParams.get('limit') || 10;

	const [colors, paginations] = await api.color.findAll(+page, +limit);

	return { colors, paginations, limit: +limit };
};
