import { colorCRUDSchema, type ColorCRUD } from '$lib/api/color';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async () => {
	const data: ColorCRUD = {
		name: '',
		hex: '#000000'
	};

	const colorForm = await superValidate(data, zod(colorCRUDSchema));

	return { colorForm };
};
