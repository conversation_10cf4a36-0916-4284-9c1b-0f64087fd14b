import { characterCRUDSchema, type CharacterCRUD } from '$lib/api/character';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async () => {
	const data: CharacterCRUD = {
		name: '',
		spawn: 'random',
		movement: 'random',
		faceDirection: 'left',
		price: 0
	};


	const characterForm = await superValidate(data, zod(characterCRUDSchema));

	return { characterForm };
};
