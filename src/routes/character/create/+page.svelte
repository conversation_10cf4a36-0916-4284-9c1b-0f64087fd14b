<script lang="ts">
	import { goto } from '$app/navigation';
	import { AppBar } from '@skeletonlabs/skeleton';
	import IconBack from 'virtual:icons/material-symbols-light/arrow-back-ios-rounded';
	import type { PageData } from './$types';
	import GeneralSection from '../_components/GeneralSection.svelte';

	export let data: PageData;
	let { characterForm } = data;
</script>

<AppBar>
	<svelte:fragment slot="lead">
		<button on:click={() => goto('/character/list')}>
			<IconBack class="cursor-pointer w-6 h-6" />
		</button>
		<h1 class="h4">New Character</h1>
	</svelte:fragment>
</AppBar>

<section class="bg-surface-100-800-token flex-1 p-4">
	<GeneralSection superValidated={characterForm} />
</section>
