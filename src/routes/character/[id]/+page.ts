import api from '$lib/api';
import { characterCRUDSchema, type CharacterCRUD } from '$lib/api/character';
import { APP_DEPENDS } from '$lib/config';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async ({ depends, params }) => {
	depends(APP_DEPENDS.updateApplication);

	const data: CharacterCRUD = {
		id: '',
		name: '',
		description: null,
		spawn: 'random',
		movement: 'random',
		faceDirection: 'left',
		price: 0
	};

	const { id, name, description, spawn, movement, faceDirection, price } =
		await api.character.findOne(params.id);
	Object.assign(data, { id, name, description, spawn, movement, faceDirection, price });

	const characterForm = await superValidate(data, zod(characterCRUDSchema));

	return { characterForm, characterId: params.id };
};
