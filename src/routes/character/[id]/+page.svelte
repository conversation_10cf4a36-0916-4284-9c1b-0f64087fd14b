<script lang="ts">
	import { goto } from '$app/navigation';
	import { AppBar, Tab, TabGroup } from '@skeletonlabs/skeleton';
	import IconBack from 'virtual:icons/material-symbols-light/arrow-back-ios-rounded';
	import GeneralSection from '../_components/GeneralSection.svelte';
	import ColorSection from '../_components/ColorSection.svelte';

	export let data;
	let { characterForm, characterId } = data;

	let tab: 'general' | 'color' = 'general';
</script>

<AppBar>
	<svelte:fragment slot="lead"> 
		<button on:click={() => goto('/character/list')}>
			<IconBack class="cursor-pointer w-6 h-6" />
		</button>
		<h1 class="h4">
			{characterForm.data.name}
		</h1>
	</svelte:fragment>
</AppBar>

<section class="bg-surface-100-800-token flex-1 p-4">
	<TabGroup class="h-full flex flex-col" regionPanel="flex-1">
		<Tab bind:group={tab} name="General" value="general">General</Tab>
		<Tab bind:group={tab} name="Color" value="color">Colour</Tab>

		<svelte:fragment slot="panel">
			{#if tab === 'general'}
				<GeneralSection formType="update" superValidated={characterForm} />
			{:else if tab === 'color'}
				<ColorSection characterId={characterId}/>
			{/if}
		</svelte:fragment>
	</TabGroup>
</section>
