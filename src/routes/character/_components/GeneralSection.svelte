<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { characterCRUDSchema, type CharacterCRUD } from '$lib/api/character';
	import CustomNumberInput from '$lib/components/form/CustomNumberInput.svelte';
	import InputWrapper from '$lib/components/form/InputWrapper.svelte';
	import SelectInput from '$lib/components/form/SelectInput.svelte';
	import TextAreaInput from '$lib/components/form/TextAreaInput.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';

	export let formType: 'create' | 'update' = 'create';
	export let superValidated: SuperValidated<CharacterCRUD>;

	const positionOptions = [
		{
			label: 'Random',
			value: 'random'
		},
		{
			label: 'On Ground',
			value: 'ground'
		}
	];

	const directionOptions = [
		{
			label: 'Left',
			value: 'left'
		},
		{
			label: 'Right',
			value: 'right'
		}
	];

	const toastStore = getToastStore();

	const sForm = superForm(superValidated, {
		SPA: true,
		validators: zod(characterCRUDSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					switch (formType) {
						case 'create': {
							await api.character.create(data);
							break;
						}

						case 'update': {
							const { id, ...dto } = data;
							await api.character.update(id!, dto);
							break;
						}
					}

					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
					});
					await invalidate(APP_DEPENDS.listCharacter);
					await goto('/character/list');
				}
			} catch (error) {
				return toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Something went wrong. Cannot submit form at the moment.',
					timeout: 3000
				});
			}
		}
	});
	const { enhance, tainted, form } = sForm;
</script>

<form class="h-full flex flex-col gap-y-4 p-2" method="POST" use:enhance>
	<TextInput form={sForm} field="id" label="id" hidden />

	<InputWrapper>
		<TextInput form={sForm} field="name" label="Name" />
		<TextAreaInput form={sForm} field="description" label="Description" />
		<SelectInput form={sForm} field="spawn" label="Spawn" options={positionOptions} />
		<SelectInput form={sForm} field="movement" label="Movement" options={positionOptions} />
		<SelectInput
			form={sForm}
			field="faceDirection"
			label="Initial Face Direction"
			options={directionOptions}
		/>
		<CustomNumberInput
			form={sForm}
			field="price"
			label="Price"
			minus={() => {
				form.update((values) => {
					if (values.price > 0) {
						values.price = values.price - 20;
						// values.price = values.price - 1;
					}
					return values;
				});
			}}
			plus={() => {
				form.update((values) => {
					// values.price = values.price + 1;
					values.price = values.price + 20;
					return values;
				});
			}}
		/>
	</InputWrapper>

	<button class="btn variant-filled-success" disabled={!$tainted}>Save Changes</button>
</form>
