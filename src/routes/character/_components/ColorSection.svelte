<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import {
		characterColorCRUDSchema,
		ColorType,
		type CharacterColorCRUD
	} from '$lib/api/character_color';
	import type { Color } from '$lib/api/color';
	import InputWrapper from '$lib/components/form/InputWrapper.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import type { equal } from 'assert';
	import { onMount } from 'svelte';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';

	export let characterId: string;

	const toastStore = getToastStore();

	let types: ColorType[] = [];
	let characterColors: CharacterColorCRUD[] = [];
	let colors: Color[] = [];
	let isLoading: boolean = false;

	const getCharacterColors = async () => {
		const data = await api.character_color.findAll(characterId);
		types = data.type;
		characterColors = data.characterColors;
		colors = data.colors;
	};

	const handleAssign = async (
		exist: boolean,
		newType: ColorType,
		{ type, ...characterColor }: CharacterColorCRUD
	) => {
		isLoading = true;
		if (newType !== type || !exist) {
			characterColors = characterColors.map((cc) =>
				cc.colorId === characterColor.colorId ? { ...cc, type: newType } : cc
			);
			await api.character_color.assign({ ...characterColor, type: newType });
		} else {
			await api.character_color.unassign(characterColor);
		}

		await getCharacterColors();
		isLoading = false;
	};

	onMount(() => {
		getCharacterColors();
	});
</script>

<div class="h-full flex flex-col gap-y-4 p-2 text-center">
	<div class="flex gap-2">
		<span class="uppercase w-20"></span>
		{#each types as t}
			<span class="w-20">{t}</span>
		{/each}
	</div>
	<div class="grid gap-2">
		{#each colors as color}
			{@const characterColor = characterColors.find((cc) => cc.colorId === color.id)}
			<div class="flex gap-2">
				<span class="w-20 uppercase">{color.name}</span>
				<span class="w-20"
					><input
						type="checkbox"
						checked={characterColor?.type === ColorType.REGULAR}
						disabled={isLoading}
						on:change={() =>
							handleAssign(Boolean(characterColor), ColorType.REGULAR, {
								characterId,
								colorId: color.id,
								type: characterColor ? characterColor.type : ColorType.REGULAR
							})}
					/></span
				>
				<span class="w-20"
					><input
						type="checkbox"
						checked={characterColor?.type === ColorType.PREMIUM}
						disabled={isLoading}
						on:change={() =>
							handleAssign(Boolean(characterColor), ColorType.PREMIUM, {
								characterId,
								colorId: color.id,
								type: characterColor ? characterColor.type : ColorType.PREMIUM
							})}
					/></span
				>
			</div>
		{/each}
	</div>
</div>
