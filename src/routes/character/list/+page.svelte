<script lang="ts">
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import type { PageData } from './$types';
	import type { Character } from '$lib/api/character';
	import { formatDate } from '$lib/datetime';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { goto } from '$app/navigation';
	import Table from '$lib/components/table/Table.svelte';

	export let data: PageData;
	let { characters, paginations, limit } = data;
	$: ({ characters, paginations, limit } = data);

	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<Character>;
	}): ColumnDef<Character>[] => [
		{
			accessorKey: 'name',
			header: 'Name',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'spawn',
			header: 'Spawn At',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'movement',
			header: 'Movement',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'faceDirection',
			header: 'Face Direction',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'price',
			header: 'Price',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'createdAt',
			header: 'Created At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			accessorKey: 'updatedAt',
			header: 'Last Updated At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) =>
				flexRender(TableAction, {
					buttons: [
						{
							label: 'Details',
							class: 'variant-filled-warning',
							onClick: async () => {
								goto(`/character/${info.row.original.id}`);
							}
						}
					]
				})
		}
	];
</script>

<Table data={characters} {columnsBuilder} {paginations} bind:limit>
	<svelte:fragment slot="header-trail">
		<button
			type="button"
			class="btn variant-filled-primary"
			on:click={() => goto('/character/create')}
		>
			New Character
		</button>
	</svelte:fragment>
</Table>
