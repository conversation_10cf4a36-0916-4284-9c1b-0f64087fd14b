<script lang="ts">
	import { goto } from '$app/navigation';
	import { AppBar, Tab, TabGroup } from '@skeletonlabs/skeleton';
	import IconBack from 'virtual:icons/material-symbols-light/arrow-back-ios-rounded';
	import GeneralSection from '../_components/GeneralSection.svelte';
	import ProjectionSection from '../_components/ProjectionSection.svelte';
	import SettingSection from '../_components/SettingSection.svelte';
	import type { PageData } from './$types';
	import StatisticSection from '../_components/StatisticSection.svelte';
	import ConnectSection from '../_components/ConnectSection.svelte';

	export let data: PageData;
	let { applicationForm, settingForm } = data;

	let tab: 'general' | 'setting' | 'projection' | 'statistic' = 'general';
</script>

<AppBar>
	<svelte:fragment slot="lead">
		<button on:click={() => goto('/application/list')}>
			<IconBack class="cursor-pointer w-6 h-6" />
		</button>
		<h1 class="h4">
			{applicationForm.data.name}
		</h1>
	</svelte:fragment>
</AppBar>

<section class="bg-surface-100-800-token flex-1 p-4">
	<TabGroup class="h-full flex flex-col" regionPanel="flex-1">
		<Tab bind:group={tab} name="General" value="general">General</Tab>
		<Tab bind:group={tab} name="Setting" value="setting">Setting</Tab>
		<Tab bind:group={tab} name="Projection" value="projection">Projection</Tab>
		<Tab bind:group={tab} name="Statistic" value="statistic">Statistic</Tab>
		<Tab bind:group={tab} name="Connect" value="connect">Connect</Tab>

		<svelte:fragment slot="panel">
			{#if tab === 'general'}
				<GeneralSection formType="update" superValidated={applicationForm} />
			{:else if tab === 'setting'}
				<SettingSection superValidated={settingForm} />
			{:else if tab === 'projection'}
				<ProjectionSection appId={applicationForm.data.id} />
			{:else if tab === 'statistic'}
				<StatisticSection appId={applicationForm.data.id} />
			{:else if tab === 'connect'}
				<ConnectSection appId={applicationForm.data.id} />
			{:else if import.meta.env.MODE === 'development'}
				<p>Tab not implemented yet</p>
			{/if}
		</svelte:fragment>
	</TabGroup>
</section>
