import api from '$lib/api';
import {
	applicationCRUDSchema,
	settingCRUDSchema,
	type ApplicationCRUD,
	type SettingCRUD
} from '$lib/api/application';
import { APP_DEPENDS } from '$lib/config';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async ({ depends, params }) => {
	depends(APP_DEPENDS.updateApplication);

	const applicationDto: ApplicationCRUD = {
		id: params.id,
		name: ''
	};

	const settingDto: SettingCRUD = {
		appId: params.id,
		backgroundId: '',
		projectionLockTimeout: 0,
		maxProjectionCharacterPerAccount: 0,
		minCharacters: 0,
		maxCharacters: 0,
		size: 0,
		speed: 0,
		ttlCharacter: 0
	};

	const { id, name } = await api.application.findOne(params.id);
	Object.assign(applicationDto, { id, name });

	const {
		appId,
		minCharacters,
		maxCharacters,
		ttlCharacter,
		size,
		speed,
		backgroundId,
		background,
		projectionLockTimeout,
		maxProjectionCharacterPerAccount
	} = await api.application.findOneSettings(params.id);

	Object.assign(settingDto, {
		appId,
		minCharacters,
		maxCharacters,
		ttlCharacter,
		size,
		speed,
		backgroundId,
		background,
		projectionLockTimeout,
		maxProjectionCharacterPerAccount
	});

	const applicationForm = await superValidate(applicationDto, zod(applicationCRUDSchema));

	const settingForm = await superValidate(settingDto, zod(settingCRUDSchema));

	return { applicationForm, settingForm };
};
