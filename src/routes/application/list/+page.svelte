<script lang="ts">
	import { goto } from '$app/navigation';
	import type { Application } from '$lib/api/application';
	import Table from '$lib/components/table/Table.svelte';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { formatDate } from '$lib/datetime';
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import type { PageData } from './$types';

	export let data: PageData;
	let { applications, paginations, limit } = data;
	$: ({ applications, paginations, limit } = data);

	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<Application>;
	}): ColumnDef<Application>[] => [
		{
			accessorKey: 'id',
			header: 'ID',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'name',
			header: 'Name',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'createdAt',
			header: 'Created At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			accessorKey: 'updatedAt',
			header: 'Last Updated At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) =>
				flexRender(TableAction, {
					buttons: [
						{
							label: 'Details',
							class: 'variant-filled-warning',
							onClick: async () => {
								goto(`/application/${info.row.original.id}`);
							}
						}
					]
				})
		}
	];
</script>

<Table data={applications} {columnsBuilder} {paginations} bind:limit>
	<svelte:fragment slot="header-trail">
		<button
			type="button"
			class="btn variant-filled-primary"
			on:click={() => goto('/application/create')}
		>
			New Application
		</button>
	</svelte:fragment>
</Table>
