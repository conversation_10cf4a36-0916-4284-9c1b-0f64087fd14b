import api from '$lib/api';
import { APP_DEPENDS } from '$lib/config.js';

export const prerender = 'auto';
export const load = async ({ depends, url }) => {
	depends(APP_DEPENDS.listApplication);

	const page = url.searchParams.get('page') || 1;
	const limit = url.searchParams.get('limit') || 10;

	const [applications, paginations] = await api.application.findAll(+page, +limit);

	return { applications, paginations, limit: +limit };
};
