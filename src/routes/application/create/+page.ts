import { applicationCRUDSchema, type ApplicationCRUD } from '$lib/api/application';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async () => {
	const applicationDto: ApplicationCRUD = {
		id: '',
		name: ''
	};

	const applicationForm = await superValidate(applicationDto, zod(applicationCRUDSchema));

	return { applicationForm };
};
