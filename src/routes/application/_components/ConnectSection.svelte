<script lang="ts">
	import api from '$lib/api';

	export let appId: string = '';

	let apiKey = '';
	let copied = false;
	let errorMsg = '';

	async function generateApiKey() {
		try {
			const { key } = await api.auth.generateApiKey(appId);
			apiKey = key;
		} catch (error) {
			if (error === 'Request failed with status code 422') {
				errorMsg = 'API Key already exists. Please revoke the key first.';
			}
		}
	}

	async function revokeApiKey() {
		errorMsg = '';
		await api.auth.revokeApiKey(appId);
		apiKey = '';
	}

	function copyApiKey() {
		navigator.clipboard.writeText(apiKey);
		copied = true;
		setTimeout(() => {
			copied = false;
		}, 1000);
	}
</script>

<section class="h-full flex flex-col gap-y-4 p-2">
	<button class="btn btn-sm variant-filled-success w-fit" on:click={generateApiKey}>
		Generate API Key
	</button>

	{#if errorMsg}
		<p class="text-sm text-error-400">{errorMsg}</p>
	{/if}

	<button class="btn btn-sm variant-filled-error w-fit" on:click={revokeApiKey}>
		Revoke API Key
	</button>

	{#if apiKey}
		<div class="bg-slate-100/10 w-fit p-4 rounded-xl tex border-slate-100 border-2">
			<div class="flex items-center gap-x-2">
				<p class="text-sm">API Key: {apiKey}</p>
				<button class="btn btn-sm variant-ghost-primary" on:click={copyApiKey}>
					{copied ? 'Copied' : 'Copy'}
				</button>
			</div>
			<span class="text-sm font-bold">
				Please keep this key safe. You will not be able to see it again.
			</span>
		</div>
	{/if}
</section>
