<script lang="ts">
	import api from '$lib/api';
	import type { ProjectionStatus } from '$lib/api/application';
	import { onMount } from 'svelte';

	export let appId: string = '';

	let projectionStatus: ProjectionStatus = {
		totalCharacters: 0,
		activeCharacters: 0,
		queueLength: 0
	};
	$: ({ totalCharacters, activeCharacters, queueLength } = projectionStatus);

	async function onCharactersFlushClick() {
		projectionStatus = await api.application.flushCharacters(appId);
	}

	async function onQueueFlushClick() {
		projectionStatus = await api.application.flushQueue(appId);
	}

	onMount(async () => {
		projectionStatus = await api.application.projectionStatus(appId);
	});
</script>

<section class="h-full flex flex-col gap-y-4 p-2">
	<div class="flex items-center gap-x-2 w-fit">
		<span class="font-semibold"> Total characters: {totalCharacters} </span>
	</div>

	<div class="flex items-center gap-x-2 w-fit">
		<span class="font-semibold"> Active characters: {activeCharacters} </span>
		<button
			class="btn btn-sm variant-filled-error w-fit"
			on:click={onCharactersFlushClick}
			disabled={activeCharacters <= 0}>Clear</button
		>
	</div>

	<div class="flex items-center gap-x-2 w-fit">
		<span class="font-semibold"> Waiting in queue: {queueLength} </span>
		<button
			class="btn btn-sm variant-filled-error w-fit"
			on:click={onQueueFlushClick}
			disabled={queueLength <= 0}
		>
			Clear
		</button>
	</div>
</section>
