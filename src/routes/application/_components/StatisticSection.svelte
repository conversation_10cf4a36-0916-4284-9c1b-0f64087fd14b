<script lang="ts">
	import { camelCaseToWords } from '$lib';
	import api from '$lib/api';
	import { dayjsTz } from '$lib/datetime';
	import {
		RecursiveTreeView,
		popup,
		type TreeViewNode,
		getToastStore
	} from '@skeletonlabs/skeleton';
	import { <PERSON>uff<PERSON> } from 'buffer';
	import { onMount } from 'svelte';

	export let appId: string = '';

	const toastStore = getToastStore();

	let dialogEl: HTMLDialogElement | null = null;
	let treeViewStatistic: (TreeViewNode & { show?: boolean })[] = [];
	let showHideStatisticDate: { label: string; show: boolean }[] = [];
	let fromDateString = dayjsTz().format('YYYY-MM-DD');
	let toDateString = dayjsTz().format('YYYY-MM-DD');
	let isFetching = false;
	let isDownloading = false;
	let filePwd = '';
	let copied = false;

	let reportFilter = [
		{
			type: 'kiosk',
			label: 'Kiosk',
			show: true
		},
		{
			type: 'webapp',
			label: 'Mobile Web App',
			show: true
		},
		{
			type: 'physical',
			label: 'Physical Paper',
			show: true
		}
	];

	const getStatisticData = async () => {
		isFetching = true;

		const statistic = await api.report.generate({
			appId,
			from: dayjsTz(fromDateString).toDate(),
			to: dayjsTz(toDateString).toDate()
		});

		const { kiosk, webapp, physical } = statistic;

		const kioskTreeViewStatistic = Object.entries(kiosk).map<TreeViewNode>(
			([date, statisticDetail]) => {
				return {
					id: `Kiosk_${date}`,
					content: `Kiosk | ${dayjsTz(date).format('dddd DD/MM/YYYY')}`,
					value: date,
					show: true,
					children: Object.entries(statisticDetail).map<TreeViewNode>(([key, value]) => ({
						id: `${key}-${Math.round(Date.now() * Math.random())}`,
						content:
							typeof value === 'number'
								? `${camelCaseToWords(key)}: ${value}`
								: camelCaseToWords(key),
						value: key,
						children:
							typeof value !== 'number'
								? Object.entries(value).map(([key, value]) => ({
										id: `${key}-${Math.round(Date.now() * Math.random())}`,
										content: `${camelCaseToWords(key)}: ${value}`,
										value: key
									}))
								: undefined
					}))
				};
			}
		);

		const webAppTreeViewStatistic = Object.entries(webapp).map<TreeViewNode>(
			([date, statisticDetail]) => {
				return {
					id: `Webapp_${date}`,
					content: `Webapp | ${dayjsTz(date).format('dddd DD/MM/YYYY')}`,
					value: date,
					show: true,
					children: Object.entries(statisticDetail).map<TreeViewNode>(([key, value]) => ({
						id: `${key}-${Math.round(Date.now() * Math.random())}`,
						content:
							typeof value === 'number'
								? `${camelCaseToWords(key)}: ${value}`
								: camelCaseToWords(key),
						value: key,
						children:
							typeof value !== 'number'
								? Object.entries(value).map(([key, value]) => ({
										id: `${key}-${Math.round(Date.now() * Math.random())}`,
										content: `${camelCaseToWords(key)}: ${value}`,
										value: key
									}))
								: undefined
					}))
				};
			}
		);

		const physicalTreeViewStatistic = Object.entries(physical).map<TreeViewNode>(
			([date, statisticDetail]) => {
				return {
					id: `Physical_${date}`,
					content: `Physical | ${dayjsTz(date).format('dddd DD/MM/YYYY')}`,
					value: date,
					show: true,
					children: Object.entries(statisticDetail).map<TreeViewNode>(([key, value]) => ({
						id: `${key}-${Math.round(Date.now() * Math.random())}`,
						content:
							typeof value === 'number'
								? `${camelCaseToWords(key)}: ${value}`
								: camelCaseToWords(key),
						value: key,
						children:
							typeof value !== 'number'
								? Object.entries(value).map(([key, value]) => ({
										id: `${key}-${Math.round(Date.now() * Math.random())}`,
										content: `${camelCaseToWords(key)}: ${value}`,
										value: key
									}))
								: undefined
					}))
				};
			}
		);

		treeViewStatistic = kioskTreeViewStatistic
			.map((_, i) => [
				reportFilter[0].show ? kioskTreeViewStatistic[i] : undefined,
				reportFilter[1].show ? webAppTreeViewStatistic[i] : undefined,
				reportFilter[2].show ? physicalTreeViewStatistic[i] : undefined
			])
			.flat()
			.filter((stat) => stat !== undefined)
			.filter(({ value }) => {
				const date = dayjsTz(String(value)).format('YYYY-MM-DD');
				return showHideStatisticDate.find(({ label }) => label === date)?.show;
			});

		isFetching = false;
	};

	$: if (dayjsTz(toDateString).diff(fromDateString) < 0) {
		toDateString = fromDateString;
	}

	const generateDateMap = (from: string, to: string) => {
		const dateMap: { label: string; show: boolean }[] = [];
		let currentDate = dayjsTz(from).startOf('day');

		while (currentDate.diff(to) <= 0) {
			dateMap.push({
				label: currentDate.format('YYYY-MM-DD'),
				show: true
			});
			currentDate = currentDate.add(1, 'day');
		}

		return dateMap;
	};
	$: showHideStatisticDate = generateDateMap(fromDateString, toDateString);

	const handleShowDataClick = async () => {
		getStatisticData();
	};

	const handleDownloadClick = async () => {
		try {
			isDownloading = true;

			const { file, pwd } = await api.report.downloadExcel({
				appId,
				from: dayjsTz(fromDateString).toDate(),
				to: dayjsTz(toDateString).toDate()
			});

			const blob = new Blob([Buffer.from(file)], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			});
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			document.body.appendChild(a);
			a.setAttribute('style', 'display: none');
			a.href = url;
			a.download = `${dayjsTz(fromDateString).format('DD.MM.YYYY')}-${dayjsTz(toDateString).format(
				'DD.MM.YYYY'
			)}.zip`;
			a.click();
			document.body.removeChild(a);

			filePwd = pwd;
			dialogEl?.showModal();
		} catch (error) {
			toastStore.trigger({
				background: 'variant-filled-error',
				message: 'Something went wrong. Cannot download the report at the moment.',
				timeout: 3000
			});
		} finally {
			isDownloading = false;
		}
	};

	const handleDownloadV2Click = async () => {
		try {
			isDownloading = true;

			const { file, pwd } = await api.report.downloadExcelV2({
				appId,
				from: dayjsTz(fromDateString).toDate(),
				to: dayjsTz(toDateString).toDate()
			});

			const blob = new Blob([Buffer.from(file)], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			});
			const url = window.URL.createObjectURL(blob);
			const a = document.createElement('a');
			document.body.appendChild(a);
			a.setAttribute('style', 'display: none');
			a.href = url;
			a.download = `${dayjsTz(fromDateString).format('DD.MM.YYYY')}-${dayjsTz(toDateString).format(
				'DD.MM.YYYY'
			)}.zip`;
			a.click();
			document.body.removeChild(a);

			filePwd = pwd;
			dialogEl?.showModal();
		} catch (error) {
			toastStore.trigger({
				background: 'variant-filled-error',
				message: 'Something went wrong. Cannot download the report at the moment.',
				timeout: 3000
			});
		} finally {
			isDownloading = false;
		}
	};

	onMount(() => {
		getStatisticData();
	});
</script>

<!-- svelte-ignore a11y-no-static-element-interactions -->
<!-- svelte-ignore a11y-click-events-have-key-events -->
<section class="h-full flex flex-col gap-y-4 p-2">
	<section class="flex gap-x-4 w-fit">
		<div class="flex gap-x-2 items-center">
			<span class="font-semibold">From: </span>
			<input
				class="input w-48"
				type="date"
				min="2024-01-01"
				max={dayjsTz().format('YYYY-MM-DD')}
				on:change={() => {
					if (dayjsTz(toDateString).diff(fromDateString) > 30) {
						toDateString = dayjsTz(fromDateString).add(30, 'days').format('YYYY-MM-DD');
					}
				}}
				bind:value={fromDateString}
			/>
		</div>

		<div class="divider-vertical" />

		<div class="flex gap-x-2 items-center">
			<span class="font-semibold">To: </span>
			<input
				class="input w-48"
				type="date"
				min={fromDateString}
				max={dayjsTz(fromDateString).add(30, 'days').format('YYYY-MM-DD')}
				lang="en-us"
				bind:value={toDateString}
			/>
		</div>

		<div class="divider-vertical" />

		<div>
			<button
				class="btn variant-filled-warning font-semibold"
				use:popup={{ event: 'focus-click', target: 'showHide-date', placement: 'bottom' }}
			>
				Show/Hide Date: {showHideStatisticDate.filter(({ show }) => show).length}
			</button>
			<div
				class="card p-4 variant-filled-surface space-y-4 z-50 h-1/2 overflow-auto"
				data-popup="showHide-date"
			>
				<div class="flex gap-x-2">
					<div
						class="btn btn-sm variant-ghost-success cursor-pointer"
						on:click={() => {
							treeViewStatistic = treeViewStatistic.map((stat) => {
								stat.show = true;
								return stat;
							});
						}}
					>
						Check All
					</div>
					<div
						class="btn btn-sm variant-ghost-error cursor-pointer"
						on:click={() => {
							treeViewStatistic = treeViewStatistic.map((stat) => {
								stat.show = false;
								return stat;
							});
						}}
					>
						Uncheck All
					</div>
				</div>

				{#each showHideStatisticDate as { label, show }}
					<label class="flex items-center space-x-2">
						<input class="checkbox" type="checkbox" bind:checked={show} />
						<p>{dayjsTz(label).format('ddd DD/MM/YYYY')}</p>
					</label>
				{/each}
			</div>
		</div>

		<button
			class="btn variant-filled-success font-semibold"
			on:click={handleShowDataClick}
			disabled={isFetching}
		>
			{isFetching ? 'Fetching Data' : 'Show Data'}
		</button>

		<button
			class="btn variant-filled-success font-semibold"
			on:click={handleDownloadClick}
			disabled={isDownloading}
		>
			{isDownloading ? 'Downloading' : 'Download'}
		</button>

		<button
			class="btn variant-filled-success font-semibold"
			on:click={handleDownloadV2Click}
			disabled={isDownloading}
		>
			{isDownloading ? 'Downloading V2' : 'Download V2'}
		</button>
	</section>

	<p class="text-sm">
		Note: To ensure report downloadability, please select a date range of 31 days or fewer.
	</p>

	<section class="flex gap-x-4 w-fit">
		Filter by Report Type:
		{#each reportFilter as { label, show }}
			<label class="flex items-center space-x-2">
				<input class="checkbox" type="checkbox" bind:checked={show} />
				<p>{label}</p>
			</label>
		{/each}
	</section>

	<section class="overflow-auto">
		<section class="w-fit">
			<RecursiveTreeView
				width="w-full flex [&>:not([hidden])~:not([hidden])]:divider-vertical [&>:not([hidden])~:not([hidden])]:mx-0 [&>:not([hidden])]:w-96"
				spacing="space-y-0"
				nodes={treeViewStatistic.filter(({ show }) => show)}
				expandedNodes={treeViewStatistic.map(({ id }) => id)}
			/>
		</section>
	</section>
</section>

<dialog bind:this={dialogEl} class="rounded-xl">
	<div class="p-4 flex flex-col gap-y-4 min-w-96">
		<p class="text-center h3">File password: {filePwd}</p>
		<p class="text-center h4">
			Please keep this password in a safe place. You will never be able to get this password again.
		</p>

		<div class="flex gap-x-4 items-center w-full">
			<button
				class="btn btn-sm variant-filled-primary flex-1"
				type="button"
				on:click={() => {
					window.navigator.clipboard.writeText(filePwd);
					copied = true;

					setTimeout(() => {
						copied = false;
					}, 3000);
				}}
			>
				{copied ? 'Copied' : 'Copy'}
			</button>

			<button
				class="btn btn-sm variant-filled-error flex-1"
				type="button"
				on:click={() => {
					filePwd = '';
					dialogEl?.close();
				}}
			>
				Close
			</button>
		</div>
	</div>
</dialog>
