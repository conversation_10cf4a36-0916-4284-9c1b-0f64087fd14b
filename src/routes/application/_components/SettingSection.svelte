<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { settingCRUDSchema, type SettingCRUD } from '$lib/api/application';
	import { MediaType } from '$lib/api/upload';
	import InputWrapper from '$lib/components/form/InputWrapper.svelte';
	import MediaSelectInput from '$lib/components/form/MediaSelectInput.svelte';
	import NumberInput from '$lib/components/form/NumberInput.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';

	export let superValidated: SuperValidated<SettingCRUD>;

	const toastStore = getToastStore();

	const sForm = superForm(superValidated, {
		SPA: true,
		dataType: 'json',
		validators: zod(settingCRUDSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					const { appId, background, ...dto } = data;
					await api.application.updateSettings(appId, dto);
					setTimeout(async () => {
						await invalidate(APP_DEPENDS.updateApplication);
					}, 1);

					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
						timeout: 3000
					});
				}
			} catch (error) {
				return toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Something went wrong. Cannot submit form at the moment.',
					timeout: 3000
				});
			}
		}
	});
	const { form, enhance, tainted } = sForm;
</script>

<form class="h-full flex flex-col gap-y-4 p-2" method="POST" use:enhance>
	<TextInput form={sForm} field="appId" label="appId" hidden />

	<section class="grid grid-cols-2 w-fit gap-4">
		<InputWrapper>
			<MediaSelectInput
				background={$form.background}
				bind:mediaId={$form.backgroundId}
				type={[MediaType.VIDEO]}
			/>

			<NumberInput
				form={sForm}
				field="minCharacters"
				label="Minimum amount of characters on screen"
				max={$form.maxCharacters}
			/>
			<NumberInput
				form={sForm}
				field="maxCharacters"
				label="Maximum amount of characters on screen"
				min={$form.minCharacters}
			/>
			<NumberInput
				form={sForm}
				field="ttlCharacter"
				label="Minimum time to live (seconds) on screen"
			/>

			<NumberInput form={sForm} field="size" label="Character size" step={0.01} min={0.1} />

			<NumberInput form={sForm} field="speed" label="Character speed" step={0.1} min={0.1} />
		</InputWrapper>

		<InputWrapper>
			<NumberInput
				form={sForm}
				field="maxProjectionCharacterPerAccount"
				label="Maximum projection characters per account"
			/>
			<NumberInput
				form={sForm}
				field="projectionLockTimeout"
				label="Projection lock timeout (seconds)"
			/>
		</InputWrapper>
	</section>

	<button class="btn variant-filled-success" disabled={!$tainted}>Save Changes</button>
</form>
