<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { applicationCRUDSchema, type ApplicationCRUD } from '$lib/api/application';
	import InputWrapper from '$lib/components/form/InputWrapper.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import { isAxiosError } from 'axios';
	import type { SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';
	import { superForm } from 'sveltekit-superforms/client';

	export let formType: 'create' | 'update' = 'create';
	export let superValidated: SuperValidated<ApplicationCRUD>;

	const toastStore = getToastStore();

	const sForm = superForm(superValidated, {
		id: `${formType}-application`,
		SPA: true,
		validators: zod(applicationCRUDSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					switch (formType) {
						case 'create': {
							await api.application.create(data);
							await invalidate(APP_DEPENDS.listApplication);
							await goto('/application/list');
							break;
						}

						case 'update': {
							const { id, ...dto } = data;
							await api.application.update(id, dto);
							await invalidate(APP_DEPENDS.updateApplication);
							break;
						}
					}

					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
						timeout: 3000
					});
				}
			} catch (error) {
				if (isAxiosError(error)) {
					switch (error.response?.status) {
						case 409:
							return toastStore.trigger({
								background: 'variant-filled-error',
								message: 'App Id already exists. Please choose another',
								timeout: 3000
							});
						default:
							return toastStore.trigger({
								background: 'variant-filled-error',
								message: 'Something went wrong. Cannot submit form at the moment.',
								timeout: 3000
							});
					}
				}
			}
		}
	});
	const { enhance, tainted } = sForm;
</script>

<form class="h-full flex flex-col gap-y-4 p-2" method="POST" use:enhance>
	<InputWrapper>
		<TextInput form={sForm} field="id" label="id" readonly={formType === 'update' || undefined} />
		<TextInput form={sForm} field="name" label="Name" />
	</InputWrapper>

	<button class="btn variant-filled-success" disabled={!$tainted}>Save Changes</button>
</form>
