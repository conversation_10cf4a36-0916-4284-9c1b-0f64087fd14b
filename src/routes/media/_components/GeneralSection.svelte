<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { MediaType, uploadSchema, type UploadSchema } from '$lib/api/upload';
	import SelectInput from '$lib/components/form/SelectInput.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import { fileProxy, superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';

	export let formType: 'create' = 'create';
	export let form: SuperValidated<UploadSchema>;

	const toastStore = getToastStore();

	const isImage = (file: File) => file.type.includes('image');
	const isVideo = (file: File) => file.type.includes('video');

	const typeOptions = [
		{
			label: 'Video',
			value: MediaType.VIDEO
		},
		{
			label: 'Image',
			value: MediaType.IMAGE
		}
	];

	const sForm = superForm(form, {
		SPA: true,
		validators: zod(uploadSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					switch (formType) {
						case 'create': {
							await api.upload.create(data);
							break;
						}
					}

					await invalidate(APP_DEPENDS.listFaq);
					await goto('/media/list');
					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
						timeout: 3000
					});
				}
			} catch (error) {
				return toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Something went wrong. Cannot submit form at the moment.',
					timeout: 3000
				});
			}
		}
	});
	const { enhance, tainted, errors, submitting } = sForm;
	const file = fileProxy(sForm, 'file');

	const handleFileChange = (e: Event & { currentTarget: EventTarget & HTMLInputElement }) => {
		const file = e.currentTarget.files?.[0];
		if (file) {
			if (isImage(file)) {
				sForm.form.update((f) => {
					f.type = MediaType.IMAGE;
					return f;
				});
			} else if (isVideo(file)) {
				sForm.form.update((f) => {
					f.type = MediaType.VIDEO;
					return f;
				});
			}
		}
	};
</script>

<form
	class="h-full flex flex-col gap-y-4 p-2"
	method="POST"
	enctype="multipart/form-data"
	use:enhance
>
	<section class="flex-1 space-y-5">
		<SelectInput form={sForm} field="type" label="Type" options={typeOptions} disabled={true} />
		<TextInput form={sForm} field="name" label="Name" />

		<label class={'relative label w-full flex flex-col'}>
			<span>Files</span>
			<input
				type="file"
				name="images"
				accept="image/*,video/*"
				bind:files={$file}
				on:change={handleFileChange}
			/>
		</label>

		{#if $file.length}
			<div>
				<p>Preview</p>
				{#each $file as file}
					{#if file.type.includes('image')}
						<img class="w-96 h-96 object-contain" src={URL.createObjectURL(file)} alt={file.name} />
					{:else if file.type.includes('video')}
						<video class="w-96 h-96 object-contain" controls>
							<source src={URL.createObjectURL(file)} type={file.type} />
							<track kind="captions" />
						</video>
					{/if}
				{/each}
			</div>
		{/if}
	</section>

	<button class="btn variant-filled-success" disabled={!$tainted || $submitting}
		>Save Changes</button
	>
</form>
