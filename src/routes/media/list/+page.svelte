<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import Table from '$lib/components/table/Table.svelte';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import type { PageData } from './$types';
	import type { MediaItem } from '$lib/api/upload';

	export let data: PageData;
	let { paths, paginations, limit } = data;
	$: ({ paths, paginations, limit } = data);

	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<MediaItem>;
	}): ColumnDef<MediaItem>[] => [
		{
			accessorKey: 'name',
			header: 'Name',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'path',
			header: 'Path',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) =>
				flexRender(TableAction, {
					buttons: [
						{
							label: 'Preview',
							class: 'variant-filled-warning',
							onClick: async () => {
								window.open(info.row.original.path, '_blank');
							}
						},
						{
							label: 'Delete',
							class: 'variant-filled-error',
							onClick: async () => {
								await api.upload.delete(info.row.original.id);
								await invalidate(APP_DEPENDS.listProjectionsVideos);
							}
						}
					]
				})
		}
	];
</script>

<Table data={paths} {columnsBuilder} {paginations} bind:limit>
	<svelte:fragment slot="header-trail">
		<button type="button" class="btn variant-filled-primary" on:click={() => goto('/media/create')}>
			New Media
		</button>
	</svelte:fragment>
</Table>
