import api from '$lib/api';
import { APP_DEPENDS } from '$lib/config.js';

export const prerender = 'auto';
export const load = async ({ depends, url }) => {
	depends(APP_DEPENDS.listProjectionsVideos);
	const page = url.searchParams.get('page') || 1;
	const limit = url.searchParams.get('limit') || 10;

	const [paths, paginations] = await api.upload.paginate(+page, +limit);

	return { paths, paginations, limit: +limit };
};
