import { faqCRUDSchema, type FaqCRUD } from '$lib/api/faq';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { PageLoad } from './$types';

export const prerender = 'auto';
export const load: PageLoad = async () => {
	const data: FaqCRUD = {
		question: '',
		answer: '',
		order: 0
	};

	const faqForm = await superValidate(data, zod(faqCRUDSchema));
	
	return { faqForm };
};
