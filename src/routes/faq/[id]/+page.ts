import api from '$lib/api';
import { faqCRUDSchema, type FaqCRUD } from '$lib/api/faq';
import { superValidate } from 'sveltekit-superforms';
import type { PageLoad } from './$types';
import { zod } from 'sveltekit-superforms/adapters';

export const prerender = 'auto';
export const load: PageLoad = async ({ params }) => {
	const data: FaqCRUD = {
		id: '',
		question: '',
		answer: '',
		order: 0
	};

	const { id, question, answer, order } = await api.faq.findOne(params.id);
	Object.assign(data, { id, question, answer, order });

	const form = await superValidate(data, zod(faqCRUDSchema));

	return { form };
};
