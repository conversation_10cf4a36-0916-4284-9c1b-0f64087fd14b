<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import type { Faq } from '$lib/api/faq';
	import Table from '$lib/components/table/Table.svelte';
	import TableAction from '$lib/components/table/TableAction.svelte';
	import { formatDate } from '$lib/datetime';
	import { flexRender, type ColumnDef, type FilterFn } from '@tanstack/svelte-table';
	import Order from '../_components/Order.svelte';
	import type { PageData } from './$types';
	import api from '$lib/api';
	import { APP_DEPENDS } from '$lib/config';

	export let data: PageData;
	let { faqs, paginations, limit } = data;
	$: ({ faqs, paginations, limit } = data);

	const columnsBuilder = ({
		globalFilterFn
	}: {
		globalFilterFn: FilterFn<Faq>;
	}): ColumnDef<Faq>[] => [
		{
			accessorKey: 'order',
			header: 'Order',
			cell: (info) =>
				flexRender(Order, {
					order: info.getValue(),
					onUp: async () => {
						await api.faq.updateOrder(info.row.original.id, Number(info.getValue() as string) - 1);
						await invalidate(APP_DEPENDS.listFaq);
					},
					onDown: async () => {
						await api.faq.updateOrder(info.row.original.id, Number(info.getValue() as string) + 1);
						await invalidate(APP_DEPENDS.listFaq);
					}
				}),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'question',
			header: 'Question',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'answer',
			header: 'Answer',
			cell: (info) => info.getValue(),
			filterFn: globalFilterFn
		},
		{
			accessorKey: 'createdAt',
			header: 'Created At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			accessorKey: 'updatedAt',
			header: 'Updated At',
			cell: (info) => formatDate(info.getValue<Date>())
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: (info) =>
				flexRender(TableAction, {
					buttons: [
						{
							label: 'Details',
							class: 'variant-filled-warning',
							onClick: async () => {
								goto(`/faq/${info.row.original.id}`);
							}
						},
						{
							label: 'Delete',
							class: 'variant-filled-error',
							onClick: async () => {
								if (!confirm('Are you sure you want to delete this question?')) return;

								await api.faq.delete(info.row.original.id);
								await invalidate(APP_DEPENDS.listFaq);
							}
						}
					]
				})
		}
	];
</script>

<Table data={faqs} {columnsBuilder} {paginations} bind:limit>
	<svelte:fragment slot="header-trail">
		<button type="button" class="btn variant-filled-primary" on:click={() => goto('/faq/create')}>
			New Question
		</button>
	</svelte:fragment>
</Table>
