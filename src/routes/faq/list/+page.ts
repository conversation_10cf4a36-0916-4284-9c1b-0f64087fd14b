import api from '$lib/api';
import { APP_DEPENDS } from '$lib/config.js';

export const prerender = 'auto';
export const load = async ({ depends, url }) => {
	depends(APP_DEPENDS.listFaq);
	const page = url.searchParams.get('page') || 1;
	const limit = url.searchParams.get('limit') || 10;

	try {
		const [faqs, paginations] = await api.faq.findAll(+page, +limit);

		return { faqs, paginations, limit: +limit };
	} catch (error) {
		return {
			faqs: [],
			paginations: {
				isFirstPage: false,
				isLastPage: false,
				currentPage: 0,
				previousPage: null,
				nextPage: null,
				pageCount: 0,
				totalCount: +limit
			}
		};
	}
};
