<script>
	import IconUp from 'virtual:icons/material-symbols-light/keyboard-arrow-up';
	import IconDown from 'virtual:icons/material-symbols-light/keyboard-arrow-down';

	export let order = 0;
	export let onUp = () => {};
	export let onDown = () => {};
</script>

<div class="flex justify-center items-center flex-col">
	<button class="btn-icon btn-icon-sm" on:click={onUp}>
		<IconUp class="cursor-pointer w-5 h-5" />
	</button>
	<p>{order}</p>
	<button class="btn-icon btn-icon-sm" on:click={onDown}>
		<IconDown class="cursor-pointer w-5 h-5" />
	</button>
</div>
