<script lang="ts">
	import { goto, invalidate } from '$app/navigation';
	import api from '$lib/api';
	import { faqCRUDSchema, type FaqCRUD } from '$lib/api/faq';
	import TextAreaInput from '$lib/components/form/TextAreaInput.svelte';
	import TextInput from '$lib/components/form/TextInput.svelte';
	import { APP_DEPENDS } from '$lib/config';
	import { getToastStore } from '@skeletonlabs/skeleton';
	import { superForm, type SuperValidated } from 'sveltekit-superforms';
	import { zod } from 'sveltekit-superforms/adapters';

	export let formType: 'create' | 'update' = 'create';
	export let form: SuperValidated<FaqCRUD>;

	const toastStore = getToastStore();

	const sForm = superForm(form, {
		SPA: true,
		validators: zod(faqCRUDSchema),
		taintedMessage: 'Are you sure you want to leave this page? All unsaved changes will be lost.',
		async onUpdate({ form: { valid, data } }) {
			try {
				if (valid) {
					switch (formType) {
						case 'create': {
							await api.faq.create(data);
							break;
						}

						case 'update': {
							const { id, ...dto } = data;
							await api.faq.update(id!, dto);
							break;
						}
					}

					await invalidate(APP_DEPENDS.listFaq);
					await goto('/faq/list');
					toastStore.trigger({
						background: 'variant-filled-success',
						message: 'Success!',
						timeout: 3000
					});
				}
			} catch (error) {
				return toastStore.trigger({
					background: 'variant-filled-error',
					message: 'Something went wrong. Cannot submit form at the moment.',
					timeout: 3000
				});
			}
		}
	});
	const { enhance, tainted } = sForm;
</script>

<form class="h-full flex flex-col gap-y-4 p-2" method="POST" use:enhance>
	<TextInput form={sForm} field="id" label="id" hidden />

	<section class="flex-1 space-y-5">
		<!-- <Editor
			bind:value={$formStore.question}
			scriptSrc="{base}/tinymce/tinymce.min.js"
			conf={{
				menubar: true,
				plugins:
					'fullscreen searchreplace autolink link directionality visualblocks visualchars image media codesample table charmap pagebreak nonbreaking anchor insertdatetime advlist lists wordcount help charmap emoticons autosave',
				toolbar:
					'undo redo fullscreen | blocks fontfamily fontsize | bold italic underline forecolor backcolor link | alignleft aligncenter alignright alignjustify lineheight | bullist numlist indent outdent | removeformat',
				contextmenu: 'configurepermanentpen',
				skin: 'oxide',
				content_css: 'default',
				promotion: false,
				branding: false
			}}
		/> -->
		<TextAreaInput form={sForm} field="question" label="Question" />
		<TextAreaInput form={sForm} field="answer" label="Answer" />
	</section>

	<button class="btn variant-filled-success" disabled={!$tainted}>Save Changes</button>
</form>
